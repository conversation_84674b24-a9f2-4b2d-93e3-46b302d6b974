import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  users,
  posts,
  questionsSchema,
  quizSchema,
  organisations,
  pointsConfigSchema,
  notification_templates,
  scheduled_notifications,
  raffleSchema,
  student_clubs,
} from '@/db/schema';
import { eq, and, inArray, sql } from 'drizzle-orm';
import { EmailService } from '@/mail/email.service';

export interface UserDeletionResult {
  success: boolean;
  deletedUser?: {
    id: string;
    email: string;
    role: string;
  };
  cleanupStats?: {
    questionsOrphaned: number;
    questionsReassigned: number;
    quizzesOrphaned: number;
    organisationsOrphaned: number;
    pointsConfigOrphaned: number;
    notificationTemplatesOrphaned: number;
    scheduledNotificationsOrphaned: number;
    rafflesOrphaned: number;
    clubsOrphaned: number;
    studentProfileDeleted: boolean;
    postsDeleted: number;
  };
  constraintStatus?: {
    migrationApplied: boolean;
    questionsConstraintType: string;
    requiresManualCleanup: boolean;
  };
  error?: string;
}

export interface BulkUserDeletionResult {
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  successes: Array<{
    id: string;
    email: string;
    role: string;
    cleanupStats: UserDeletionResult['cleanupStats'];
  }>;
  failures: Array<{
    id: string;
    email: string;
    error: string;
  }>;
  duration: string;
  startTime: Date;
  endTime: Date;
}

@Injectable()
export class UserDeletionService {
  private readonly logger = new Logger(UserDeletionService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Apply the migration to fix foreign key constraints if needed
   * This method can be called to manually apply the constraint fixes
   */
  async applyConstraintFixes(): Promise<{
    success: boolean;
    message: string;
    appliedFixes: string[];
  }> {
    const appliedFixes: string[] = [];

    try {
      this.logger.log('Applying foreign key constraint fixes...');

      // Fix questions.created_by constraint from RESTRICT to SET NULL
      await this.drizzle.db.execute(sql`
        ALTER TABLE "questions" DROP CONSTRAINT IF EXISTS "questions_created_by_users_id_fk"
      `);
      await this.drizzle.db.execute(sql`
        ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk"
        FOREIGN KEY ("created_by") REFERENCES "public"."users"("id")
        ON DELETE SET NULL ON UPDATE NO ACTION
      `);
      appliedFixes.push('questions.created_by: RESTRICT → SET NULL');

      // Add missing foreign key constraint for quiz.created_by
      await this.drizzle.db.execute(sql`
        ALTER TABLE "quiz" DROP CONSTRAINT IF EXISTS "quiz_created_by_users_id_fk"
      `);
      await this.drizzle.db.execute(sql`
        ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_users_id_fk"
        FOREIGN KEY ("created_by") REFERENCES "public"."users"("id")
        ON DELETE SET NULL ON UPDATE NO ACTION
      `);
      appliedFixes.push(
        'quiz.created_by: Added missing constraint with SET NULL',
      );

      this.logger.log(
        `Successfully applied ${appliedFixes.length} constraint fixes`,
      );

      return {
        success: true,
        message: `Successfully applied ${appliedFixes.length} foreign key constraint fixes`,
        appliedFixes,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to apply constraint fixes: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: `Failed to apply constraint fixes: ${error.message}`,
        appliedFixes,
      };
    }
  }

  /**
   * Check the current foreign key constraint status for questions.created_by
   */
  async checkConstraintStatus(): Promise<{
    migrationApplied: boolean;
    questionsConstraintType: string;
    requiresManualCleanup: boolean;
  }> {
    try {
      const result = await this.drizzle.db.execute(sql`
        SELECT
          tc.constraint_name,
          tc.constraint_type,
          rc.delete_rule,
          rc.update_rule
        FROM information_schema.table_constraints tc
        LEFT JOIN information_schema.referential_constraints rc
          ON tc.constraint_name = rc.constraint_name
        WHERE tc.table_name = 'questions'
          AND tc.constraint_name = 'questions_created_by_users_id_fk'
          AND tc.table_schema = 'public'
      `);

      if (result.rows.length === 0) {
        this.logger.warn(
          'Foreign key constraint questions_created_by_users_id_fk not found',
        );
        return {
          migrationApplied: false,
          questionsConstraintType: 'MISSING',
          requiresManualCleanup: true,
        };
      }

      const constraint = result.rows[0] as any;
      const deleteRule = constraint.delete_rule?.toUpperCase() || 'UNKNOWN';

      this.logger.log(`Questions constraint delete rule: ${deleteRule}`);

      return {
        migrationApplied: deleteRule === 'SET NULL',
        questionsConstraintType: deleteRule,
        requiresManualCleanup: deleteRule !== 'SET NULL',
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to check constraint status: ${error.message}`,
        error.stack,
      );
      return {
        migrationApplied: false,
        questionsConstraintType: 'ERROR',
        requiresManualCleanup: true,
      };
    }
  }

  /**
   * Delete a user with question reassignment to handle foreign key constraints
   */
  async deleteUserWithQuestionReassignment(
    userIdToDelete: string,
    replacementUserEmail?: string,
    sendNotification: boolean = false,
    adminUserId?: string,
  ): Promise<UserDeletionResult> {
    try {
      // Check constraint status first
      const constraintStatus = await this.checkConstraintStatus();

      // If migration is applied and constraint is SET NULL, use regular deletion
      if (constraintStatus.migrationApplied) {
        this.logger.log('Migration applied, using regular deletion method');
        const result = await this.deleteUser(
          userIdToDelete,
          sendNotification,
          adminUserId,
        );
        return {
          ...result,
          constraintStatus,
        };
      }

      // Migration not applied, use manual cleanup approach
      this.logger.log('Migration not applied, using manual cleanup approach');

      // First, check if user exists
      const userToDelete = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, userIdToDelete),
        with: {
          student_profile: true,
          profile: true,
        },
      });

      if (!userToDelete) {
        throw new NotFoundException(`User with ID ${userIdToDelete} not found`);
      }

      // Find replacement user if email provided
      let replacementUser = null;
      if (replacementUserEmail) {
        replacementUser = await this.drizzle.db.query.users.findFirst({
          where: eq(users.email, replacementUserEmail.toLowerCase()),
        });

        if (!replacementUser) {
          throw new NotFoundException(
            `Replacement user with email ${replacementUserEmail} not found`,
          );
        }

        this.logger.log(
          `Found replacement user: ${replacementUser.id} (${replacementUser.email})`,
        );
      }

      // Perform deletion with manual question cleanup in a transaction
      const deletionResult = await this.drizzle.db.transaction(async (tx) => {
        this.logger.log(
          `Starting manual cleanup deletion for user ${userIdToDelete} (${userToDelete.email})`,
        );

        // Step 1: Handle questions created by this user
        const questionsToUpdate = await tx
          .select()
          .from(questionsSchema)
          .where(eq(questionsSchema.created_by, userIdToDelete));

        let questionsReassigned = 0;
        let questionsOrphaned = 0;

        if (questionsToUpdate.length > 0) {
          if (replacementUser) {
            // Reassign questions to replacement user
            await tx
              .update(questionsSchema)
              .set({ created_by: replacementUser.id })
              .where(eq(questionsSchema.created_by, userIdToDelete));

            questionsReassigned = questionsToUpdate.length;
            this.logger.log(
              `Reassigned ${questionsReassigned} questions to ${replacementUser.email}`,
            );
          } else {
            // Set created_by to NULL
            await tx
              .update(questionsSchema)
              .set({ created_by: null })
              .where(eq(questionsSchema.created_by, userIdToDelete));

            questionsOrphaned = questionsToUpdate.length;
            this.logger.log(
              `Orphaned ${questionsOrphaned} questions (set created_by to NULL)`,
            );
          }
        }

        // Step 2: Count other records that will be affected
        const quizzesCount = await tx
          .select()
          .from(quizSchema)
          .where(eq(quizSchema.created_by, userIdToDelete));

        const organisationsCount = await tx
          .select()
          .from(organisations)
          .where(eq(organisations.user_id, userIdToDelete));

        const pointsConfigCount = await tx
          .select()
          .from(pointsConfigSchema)
          .where(eq(pointsConfigSchema.created_by, userIdToDelete));

        const notificationTemplatesCount = await tx
          .select()
          .from(notification_templates)
          .where(eq(notification_templates.created_by, userIdToDelete));

        const scheduledNotificationsCount = await tx
          .select()
          .from(scheduled_notifications)
          .where(eq(scheduled_notifications.created_by, userIdToDelete));

        const rafflesCount = await tx
          .select()
          .from(raffleSchema)
          .where(eq(raffleSchema.createdBy, userIdToDelete));

        const clubsCount = await tx
          .select()
          .from(student_clubs)
          .where(eq(student_clubs.club_admin, userIdToDelete));

        const postsCount = await tx
          .select()
          .from(posts)
          .where(eq(posts.postedBy, userIdToDelete));

        // Step 3: Delete the user (now that questions are handled)
        const deletedUser = await tx
          .delete(users)
          .where(eq(users.id, userIdToDelete))
          .returning({
            id: users.id,
            email: users.email,
            role: users.role,
          });

        if (!deletedUser.length) {
          throw new Error('Failed to delete user');
        }

        this.logger.log(
          `Successfully deleted user ${userIdToDelete} with manual cleanup`,
        );

        return {
          deletedUser: deletedUser[0],
          cleanupStats: {
            questionsOrphaned,
            questionsReassigned,
            quizzesOrphaned: quizzesCount.length,
            organisationsOrphaned: organisationsCount.length,
            pointsConfigOrphaned: pointsConfigCount.length,
            notificationTemplatesOrphaned: notificationTemplatesCount.length,
            scheduledNotificationsOrphaned: scheduledNotificationsCount.length,
            rafflesOrphaned: rafflesCount.length,
            clubsOrphaned: clubsCount.length,
            studentProfileDeleted: !!userToDelete.student_profile,
            postsDeleted: postsCount.length,
          },
        };
      });

      // Send email notification if requested
      if (sendNotification && adminUserId) {
        await this.sendSingleUserDeletionNotification(
          deletionResult,
          adminUserId,
        );
      }

      return {
        success: true,
        deletedUser: deletionResult.deletedUser,
        cleanupStats: deletionResult.cleanupStats,
        constraintStatus,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to delete user ${userIdToDelete}: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        error: error.message,
        constraintStatus: await this.checkConstraintStatus(),
      };
    }
  }

  /**
   * Delete a single user with comprehensive cleanup and logging
   */
  async deleteUser(
    userId: string,
    sendNotification: boolean = false,
    adminUserId?: string,
  ): Promise<UserDeletionResult> {
    try {
      // First, check if user exists and get their information
      const userToDelete = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, userId),
        with: {
          student_profile: true,
          profile: true,
        },
      });

      if (!userToDelete) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Perform deletion in a transaction with comprehensive cleanup
      const deletionResult = await this.drizzle.db.transaction(async (tx) => {
        this.logger.log(
          `Starting comprehensive deletion for user ${userId} (${userToDelete.email})`,
        );

        // Step 1: Count records that will be orphaned (SET NULL)
        const questionsCount = await tx
          .select()
          .from(questionsSchema)
          .where(eq(questionsSchema.created_by, userId));

        const quizzesCount = await tx
          .select()
          .from(quizSchema)
          .where(eq(quizSchema.created_by, userId));

        const organisationsCount = await tx
          .select()
          .from(organisations)
          .where(eq(organisations.user_id, userId));

        const pointsConfigCount = await tx
          .select()
          .from(pointsConfigSchema)
          .where(eq(pointsConfigSchema.created_by, userId));

        const notificationTemplatesCount = await tx
          .select()
          .from(notification_templates)
          .where(eq(notification_templates.created_by, userId));

        const scheduledNotificationsCount = await tx
          .select()
          .from(scheduled_notifications)
          .where(eq(scheduled_notifications.created_by, userId));

        const rafflesCount = await tx
          .select()
          .from(raffleSchema)
          .where(eq(raffleSchema.createdBy, userId));

        const clubsCount = await tx
          .select()
          .from(student_clubs)
          .where(eq(student_clubs.club_admin, userId));

        // Step 2: Count records that will be deleted (CASCADE)
        const postsCount = await tx
          .select()
          .from(posts)
          .where(eq(posts.postedBy, userId));

        // Step 3: Delete the user (this will trigger all CASCADE and SET NULL operations)
        const deletedUser = await tx
          .delete(users)
          .where(eq(users.id, userId))
          .returning({
            id: users.id,
            email: users.email,
            role: users.role,
          });

        if (!deletedUser.length) {
          throw new Error('Failed to delete user');
        }

        this.logger.log(
          `Successfully deleted user ${userId} with comprehensive cleanup`,
        );

        return {
          deletedUser: deletedUser[0],
          cleanupStats: {
            questionsOrphaned: questionsCount.length,
            questionsReassigned: 0,
            quizzesOrphaned: quizzesCount.length,
            organisationsOrphaned: organisationsCount.length,
            pointsConfigOrphaned: pointsConfigCount.length,
            notificationTemplatesOrphaned: notificationTemplatesCount.length,
            scheduledNotificationsOrphaned: scheduledNotificationsCount.length,
            rafflesOrphaned: rafflesCount.length,
            clubsOrphaned: clubsCount.length,
            studentProfileDeleted: !!userToDelete.student_profile,
            postsDeleted: postsCount.length,
          },
        };
      });

      // Send email notification if requested
      if (sendNotification && adminUserId) {
        await this.sendSingleUserDeletionNotification(
          deletionResult,
          adminUserId,
        );
      }

      return {
        success: true,
        deletedUser: deletionResult.deletedUser,
        cleanupStats: deletionResult.cleanupStats,
      };
    } catch (error: any) {
      this.logger.error(
        `Failed to delete user ${userId}: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Delete multiple users in bulk with comprehensive reporting
   */
  async bulkDeleteUsers(
    userIds: string[],
    adminUserId: string,
  ): Promise<BulkUserDeletionResult> {
    const startTime = new Date();
    const successes: BulkUserDeletionResult['successes'] = [];
    const failures: BulkUserDeletionResult['failures'] = [];

    this.logger.log(
      `Starting bulk deletion of ${userIds.length} users by admin ${adminUserId}`,
    );

    // Validate that all user IDs exist first
    const existingUsers = await this.drizzle.db
      .select({ id: users.id, email: users.email, role: users.role })
      .from(users)
      .where(and(inArray(users.id, userIds), eq(users.deleted, false)));

    if (existingUsers.length === 0) {
      const endTime = new Date();
      return {
        totalProcessed: 0,
        successCount: 0,
        failureCount: 0,
        successes: [],
        failures: [],
        duration: this.calculateDuration(startTime, endTime),
        startTime,
        endTime,
      };
    }

    // Process each user deletion
    for (const user of existingUsers) {
      try {
        const result = await this.deleteUser(user.id);

        if (result.success && result.deletedUser && result.cleanupStats) {
          successes.push({
            id: result.deletedUser.id,
            email: result.deletedUser.email,
            role: result.deletedUser.role,
            cleanupStats: result.cleanupStats,
          });
        } else {
          failures.push({
            id: user.id,
            email: user.email,
            error: result.error || 'Unknown error occurred',
          });
        }
      } catch (error: any) {
        failures.push({
          id: user.id,
          email: user.email,
          error: error.message,
        });
      }
    }

    const endTime = new Date();
    const bulkResult: BulkUserDeletionResult = {
      totalProcessed: existingUsers.length,
      successCount: successes.length,
      failureCount: failures.length,
      successes,
      failures,
      duration: this.calculateDuration(startTime, endTime),
      startTime,
      endTime,
    };

    // Send email notification to admin users
    await this.sendBulkDeletionNotification(bulkResult, adminUserId);

    this.logger.log(
      `Bulk deletion completed: ${successes.length} successful, ${failures.length} failed out of ${existingUsers.length} total`,
    );

    return bulkResult;
  }

  /**
   * Send email notification about single user deletion
   */
  private async sendSingleUserDeletionNotification(
    result: { deletedUser: any; cleanupStats: any },
    adminUserId: string,
  ): Promise<void> {
    try {
      // Get admin user details
      const adminUser = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, adminUserId),
      });

      if (!adminUser) {
        this.logger.warn(
          `Admin user ${adminUserId} not found for email notification`,
        );
        return;
      }

      // Get all admin users for notification
      const adminUsers = await this.drizzle.db
        .select({ email: users.email })
        .from(users)
        .where(
          and(
            eq(users.role, 'super_admin'),
            eq(users.state, 'active'),
            eq(users.deleted, false),
          ),
        );

      if (adminUsers.length === 0) {
        this.logger.warn(
          'No active admin users found for user deletion notification',
        );
        return;
      }

      const emailContext = {
        adminName: adminUser.email.split('@')[0],
        deletedUser: result.deletedUser,
        cleanupStats: result.cleanupStats,
        completedAt: new Date().toLocaleString(),
      };

      // Send emails to all admin users
      for (const admin of adminUsers) {
        await this.emailService.sendCustomEmail({
          email: admin.email,
          subject: '🗑️ User Deletion Report - Operation Complete',
          template: 'user-deletion-report',
          context: emailContext,
        });
      }

      this.logger.log(
        `Sent user deletion notification to ${adminUsers.length} admin users`,
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to send user deletion notification: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Send email notification about bulk deletion results
   */
  private async sendBulkDeletionNotification(
    result: BulkUserDeletionResult,
    adminUserId: string,
  ): Promise<void> {
    try {
      // Get admin user details
      const adminUser = await this.drizzle.db.query.users.findFirst({
        where: eq(users.id, adminUserId),
      });

      if (!adminUser) {
        this.logger.warn(
          `Admin user ${adminUserId} not found for email notification`,
        );
        return;
      }

      // Get all admin users for notification
      const adminUsers = await this.drizzle.db
        .select({ email: users.email })
        .from(users)
        .where(
          and(
            eq(users.role, 'super_admin'),
            eq(users.state, 'active'),
            eq(users.deleted, false),
          ),
        );

      if (adminUsers.length === 0) {
        this.logger.warn(
          'No active admin users found for bulk deletion notification',
        );
        return;
      }

      const emailContext = {
        adminName: adminUser.email.split('@')[0],
        totalProcessed: result.totalProcessed,
        successCount: result.successCount,
        failureCount: result.failureCount,
        duration: result.duration,
        completedAt: result.endTime.toLocaleString(),
        failures: result.failures,
      };

      // Send emails to all admin users
      for (const admin of adminUsers) {
        await this.emailService.sendCustomEmail({
          email: admin.email,
          subject: '🗑️ Bulk User Deletion Report - Operation Complete',
          template: 'bulk-deletion-report',
          context: emailContext,
        });
      }

      this.logger.log(
        `Sent bulk deletion notification to ${adminUsers.length} admin users`,
      );
    } catch (error: any) {
      this.logger.error(
        `Failed to send bulk deletion notification: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Calculate duration between two dates in human-readable format
   */
  private calculateDuration(startTime: Date, endTime: Date): string {
    const durationMs = endTime.getTime() - startTime.getTime();
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) {
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    }

    return `${seconds}s`;
  }
}
