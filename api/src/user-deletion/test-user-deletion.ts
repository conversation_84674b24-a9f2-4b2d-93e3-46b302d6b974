/**
 * Test script to verify user deletion functionality
 * This script can be run to test the foreign key constraint handling
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserDeletionService } from './user-deletion.service';
import { Logger } from '@nestjs/common';

async function testUserDeletion() {
  const logger = new Logger('UserDeletionTest');
  
  try {
    // Create the NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    const userDeletionService = app.get(UserDeletionService);

    logger.log('🔍 Testing User Deletion Functionality');
    logger.log('=====================================');

    // Step 1: Check current constraint status
    logger.log('\n1. Checking current foreign key constraint status...');
    const constraintStatus = await userDeletionService.checkConstraintStatus();
    
    logger.log(`Migration Applied: ${constraintStatus.migrationApplied}`);
    logger.log(`Questions Constraint Type: ${constraintStatus.questionsConstraintType}`);
    logger.log(`Requires Manual Cleanup: ${constraintStatus.requiresManualCleanup}`);

    // Step 2: Apply constraint fixes if needed
    if (constraintStatus.requiresManualCleanup) {
      logger.log('\n2. Applying constraint fixes...');
      const fixResult = await userDeletionService.applyConstraintFixes();
      
      if (fixResult.success) {
        logger.log('✅ Constraint fixes applied successfully!');
        fixResult.appliedFixes.forEach(fix => logger.log(`   - ${fix}`));
      } else {
        logger.error('❌ Failed to apply constraint fixes:', fixResult.message);
      }

      // Re-check constraint status
      logger.log('\n3. Re-checking constraint status after fixes...');
      const newConstraintStatus = await userDeletionService.checkConstraintStatus();
      logger.log(`Migration Applied: ${newConstraintStatus.migrationApplied}`);
      logger.log(`Questions Constraint Type: ${newConstraintStatus.questionsConstraintType}`);
      logger.log(`Requires Manual Cleanup: ${newConstraintStatus.requiresManualCleanup}`);
    } else {
      logger.log('\n2. ✅ Constraints are already properly configured!');
    }

    logger.log('\n🎉 User deletion functionality test completed!');
    logger.log('\nNext steps:');
    logger.log('1. Try deleting a user using the enhanced method');
    logger.log('2. Check the logs for detailed cleanup information');
    logger.log('3. Verify that questions are properly handled (orphaned or reassigned)');

    await app.close();
  } catch (error: any) {
    logger.error('❌ Test failed:', error.message);
    logger.error(error.stack);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testUserDeletion().catch(console.error);
}

export { testUserDeletion };
