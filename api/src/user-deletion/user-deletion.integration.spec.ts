import { Test, TestingModule } from '@nestjs/testing';
import { UserDeletionService } from './user-deletion.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EmailService } from '@/mail/email.service';
import { QueueService } from '@app/shared/queue/queue.service';
import { users, student_profiles, questionsSchema, quizSchema } from '@/db/schema';
import { eq } from 'drizzle-orm';

describe('UserDeletionService Integration', () => {
  let service: UserDeletionService;
  let drizzleService: DrizzleService;
  let testUserId: string;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserDeletionService,
        DrizzleService,
        {
          provide: EmailService,
          useValue: {
            sendCustomEmail: jest.fn(),
          },
        },
        {
          provide: QueueService,
          useValue: {
            addSingleEmailJob: jest.fn(),
            addBulkEmailJob: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserDeletionService>(UserDeletionService);
    drizzleService = module.get<DrizzleService>(DrizzleService);
  });

  beforeEach(async () => {
    // Create a test user for each test
    const [testUser] = await drizzleService.db
      .insert(users)
      .values({
        email: `test-${Date.now()}@example.com`,
        role: 'student',
        state: 'active',
      })
      .returning({ id: users.id });

    testUserId = testUser.id;

    // Create a student profile for the test user
    await drizzleService.db.insert(student_profiles).values({
      user_id: testUserId,
      first_name: 'Test',
      last_name: 'User',
      country_id: 'test-country-id', // This would need to be a valid country ID in real tests
      institution_id: 'test-institution-id', // This would need to be a valid institution ID in real tests
      enrollment_date: 2023,
      graduation_date: 2027,
    });
  });

  afterEach(async () => {
    // Clean up any remaining test data
    try {
      await drizzleService.db.delete(users).where(eq(users.id, testUserId));
    } catch (error) {
      // User might already be deleted by the test
    }
  });

  describe('Foreign Key Constraint Handling', () => {
    it('should handle questions with SET NULL constraint', async () => {
      // Create a question created by the test user
      const [question] = await drizzleService.db
        .insert(questionsSchema)
        .values({
          question: 'Test question',
          option_a: 'A',
          option_b: 'B',
          option_c: 'C',
          option_d: 'D',
          answers: 'A',
          questionBank_id: 'test-question-bank-id', // This would need to be a valid question bank ID
          created_by: testUserId,
        })
        .returning({ id: questionsSchema.id });

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats?.questionsOrphaned).toBe(1);

      // Verify the question still exists but with created_by set to NULL
      const remainingQuestion = await drizzleService.db.query.questionsSchema.findFirst({
        where: eq(questionsSchema.id, question.id),
      });

      expect(remainingQuestion).toBeDefined();
      expect(remainingQuestion?.created_by).toBeNull();
    });

    it('should handle quiz with SET NULL constraint', async () => {
      // Create a quiz created by the test user
      const [quiz] = await drizzleService.db
        .insert(quizSchema)
        .values({
          title: 'Test Quiz',
          question_bank_id: 'test-question-bank-id', // This would need to be a valid question bank ID
          created_by: testUserId,
          total_questions: 5,
        })
        .returning({ id: quizSchema.id });

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats?.quizzesOrphaned).toBe(1);

      // Verify the quiz still exists but with created_by set to NULL
      const remainingQuiz = await drizzleService.db.query.quizSchema.findFirst({
        where: eq(quizSchema.id, quiz.id),
      });

      expect(remainingQuiz).toBeDefined();
      expect(remainingQuiz?.created_by).toBeNull();
    });

    it('should cascade delete student profile', async () => {
      // Verify student profile exists before deletion
      const profileBefore = await drizzleService.db.query.student_profiles.findFirst({
        where: eq(student_profiles.user_id, testUserId),
      });
      expect(profileBefore).toBeDefined();

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats?.studentProfileDeleted).toBe(true);

      // Verify student profile is deleted
      const profileAfter = await drizzleService.db.query.student_profiles.findFirst({
        where: eq(student_profiles.user_id, testUserId),
      });
      expect(profileAfter).toBeUndefined();
    });

    it('should handle user deletion without foreign key violations', async () => {
      // This test verifies that the migration fixed the foreign key constraints
      // and users can be deleted without constraint violations

      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.deletedUser).toBeDefined();
      expect(result.deletedUser?.id).toBe(testUserId);

      // Verify user is actually deleted
      const deletedUser = await drizzleService.db.query.users.findFirst({
        where: eq(users.id, testUserId),
      });
      expect(deletedUser).toBeUndefined();
    });
  });

  describe('Cleanup Statistics', () => {
    it('should provide accurate cleanup statistics', async () => {
      // Create some related data
      await drizzleService.db.insert(questionsSchema).values({
        question: 'Test question 1',
        option_a: 'A',
        option_b: 'B',
        option_c: 'C',
        option_d: 'D',
        answers: 'A',
        questionBank_id: 'test-question-bank-id',
        created_by: testUserId,
      });

      await drizzleService.db.insert(questionsSchema).values({
        question: 'Test question 2',
        option_a: 'A',
        option_b: 'B',
        option_c: 'C',
        option_d: 'D',
        answers: 'B',
        questionBank_id: 'test-question-bank-id',
        created_by: testUserId,
      });

      // Delete the user
      const result = await service.deleteUser(testUserId);

      expect(result.success).toBe(true);
      expect(result.cleanupStats).toBeDefined();
      expect(result.cleanupStats?.questionsOrphaned).toBe(2);
      expect(result.cleanupStats?.studentProfileDeleted).toBe(true);
    });
  });
});

// Note: This integration test requires:
// 1. A test database setup
// 2. Valid foreign key references (country_id, institution_id, question_bank_id)
// 3. The migration 0018_fix-remaining-user-deletion-constraints.sql to be applied
// 
// In a real test environment, you would:
// 1. Set up test data fixtures with valid foreign key references
// 2. Use a test database that mirrors the production schema
// 3. Run migrations before tests
// 4. Clean up test data after each test
